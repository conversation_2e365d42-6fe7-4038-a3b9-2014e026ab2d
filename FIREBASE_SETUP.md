# Firebase Setup Guide for SMS Divert App

## Overview
This guide will help you set up Firebase for the SMS Divert app to enable Firebase Cloud Messaging (FCM) tokens that are submitted to your API when users register their phone numbers.

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `sms-divert-app` (or your preferred name)
4. Enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Add Android App to Firebase

1. In your Firebase project, click "Add app" and select Android
2. Enter your Android package name: `com.example.message_divert`
3. Enter app nickname: `SMS Divert`
4. Download the `google-services.json` file
5. Place the `google-services.json` file in `android/app/` directory

## Step 3: Configure Firebase CLI

Run the following command to configure Firebase for your Flutter project:

```bash
flutterfire configure
```

This will:
- Generate the `firebase_options.dart` file with your project configuration
- Update the file with your actual Firebase project credentials

## Step 4: Update firebase_options.dart

After running `flutterfire configure`, your `lib/firebase_options.dart` file will be automatically updated with your actual Firebase project credentials. The current file contains placeholder values that need to be replaced.

## Step 5: Enable Firebase Cloud Messaging

1. In Firebase Console, go to "Cloud Messaging"
2. No additional setup required - FCM is enabled by default

## Step 6: Update API Endpoint

Update the API endpoint in your app to receive Firebase tokens:

In `lib/main.dart`, find the `_submitToApi` method and update the URL:

```dart
const String apiUrl = 'https://your-actual-api-endpoint.com/register';
```

## API Payload Format

When a user submits their phone number, the app will send this payload to your API:

```json
{
  "phone_number": "09123456789",
  "firebase_token": "firebase_fcm_token_here",
  "device_type": "android",
  "registered_at": 1234567890
}
```

## Step 7: Test Firebase Token Generation

1. Build and run the app
2. Check the console logs for "Firebase Token: ..." message
3. Enter a phone number and tap "تایید" (Confirm)
4. Check your API logs to verify the token was submitted

## Troubleshooting

### Common Issues:

1. **"Default FirebaseApp is not initialized"**
   - Make sure `google-services.json` is in `android/app/`
   - Run `flutterfire configure` to generate proper configuration

2. **Firebase token is null**
   - Check if notification permissions are granted
   - Verify Firebase project configuration
   - Test on a real device (not emulator)

3. **Build errors**
   - Run `flutter clean` and `flutter pub get`
   - Ensure all Firebase dependencies are properly installed

### Required Files:
- `android/app/google-services.json` (from Firebase Console)
- `lib/firebase_options.dart` (generated by flutterfire configure)

### Testing on Real Device:
Firebase tokens work best on real Android devices. Emulators may not generate valid tokens.

## Security Notes

- Keep your `google-services.json` file secure
- Don't commit sensitive Firebase configuration to public repositories
- Use environment variables for production API endpoints

## Next Steps

After Firebase is configured:
1. Test token generation on a real device
2. Verify your API receives the Firebase token
3. Implement push notifications if needed
4. Set up Firebase Analytics (optional)
