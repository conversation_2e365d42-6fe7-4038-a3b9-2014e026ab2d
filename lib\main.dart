import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'sms_service.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  await initializeService();
  runApp(const MyApp());
}

Future<void> initializeService() async {
  final service = FlutterBackgroundService();

  await service.configure(
    androidConfiguration: AndroidConfiguration(
      onStart: onStart,
      autoStart: true,
      isForegroundMode: true,
    ),
    iosConfiguration: IosConfiguration(
      autoStart: true,
      onForeground: onStart,
      onBackground: onIosBackground,
    ),
  );
}

@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  // Background service logic will be implemented here
  if (service is AndroidServiceInstance) {
    service.on('setAsForeground').listen((event) {
      service.setAsForegroundService();
    });

    service.on('setAsBackground').listen((event) {
      service.setAsBackgroundService();
    });
  }

  service.on('stopService').listen((event) {
    service.stopSelf();
  });
}

@pragma('vm:entry-point')
Future<bool> onIosBackground(ServiceInstance service) async {
  return true;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SMS Divert',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const PhoneNumberPage(),
    );
  }
}

class PhoneNumberPage extends StatefulWidget {
  const PhoneNumberPage({super.key});

  @override
  State<PhoneNumberPage> createState() => _PhoneNumberPageState();
}

class _PhoneNumberPageState extends State<PhoneNumberPage> {
  final TextEditingController _phoneController = TextEditingController();
  final SmsService _smsService = SmsService();
  String? _savedPhoneNumber;
  bool _isLoading = false;
  String? _firebaseToken;

  @override
  void initState() {
    super.initState();
    _loadSavedPhoneNumber();
    _requestPermissions();
    _setupSmsListener();
    _setupSmsCallback();
    _initializeFirebaseToken();
  }

  Future<void> _initializeFirebaseToken() async {
    try {
      // Request permission for notifications
      FirebaseMessaging messaging = FirebaseMessaging.instance;

      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get the token
        String? token = await messaging.getToken();
        setState(() {
          _firebaseToken = token;
        });
        print('Firebase Token: $token');
      }
    } catch (e) {
      print('Error getting Firebase token: $e');
    }
  }

  void _setupSmsCallback() {
    // Set up callback to show snackbar when SMS is received
    SmsService.onSmsReceived = (String sender, String message) {
      if (mounted) {
        // Truncate message if too long for snackbar
        String messagePreview = message.length > 30
            ? '${message.substring(0, 30)}...'
            : message;

        _showDetailedSnackBar(
          'پیامک دریافت شد از: $sender',
          'متن: $messagePreview'
        );
      }
    };
  }

  Future<void> _loadSavedPhoneNumber() async {
    final prefs = await SharedPreferences.getInstance();
    final savedNumber = prefs.getString('phone_number');
    if (savedNumber != null) {
      setState(() {
        _savedPhoneNumber = savedNumber;
        _phoneController.text = savedNumber;
      });
    }
  }

  Future<void> _requestPermissions() async {
    await [
      Permission.sms,
      Permission.phone,
    ].request();

    // Also request telephony permissions
    await _smsService.requestPermissions();
  }

  void _setupSmsListener() {
    _smsService.setupSmsListener();
  }

  Future<void> _savePhoneNumber() async {
    if (_phoneController.text.isEmpty) {
      _showSnackBar('لطفا شماره موبایل را وارد کنید');
      return;
    }

    if (!_isValidIranianNumber(_phoneController.text)) {
      _showSnackBar('لطفا شماره موبایل ایرانی معتبر وارد کنید (09xxxxxxxxx)');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('phone_number', _phoneController.text);

      setState(() {
        _savedPhoneNumber = _phoneController.text;
        _isLoading = false;
      });

      // Submit phone number and Firebase token to API
      await _submitToApi(_phoneController.text, _firebaseToken);

      _showSnackBar('شماره موبایل با موفقیت ذخیره شد!');

      // Start the background service
      final service = FlutterBackgroundService();
      var isRunning = await service.isRunning();
      if (!isRunning) {
        service.startService();
      }

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('خطا در ذخیره شماره موبایل: $e');
    }
  }

  Future<void> _submitToApi(String phoneNumber, String? firebaseToken) async {
    try {
      // Replace with your actual API endpoint
      const String apiUrl = 'https://your-api-endpoint.com/register';

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phone_number': phoneNumber,
          'firebase_token': firebaseToken,
          'device_type': 'android',
          'registered_at': DateTime.now().millisecondsSinceEpoch,
        }),
      );

      if (response.statusCode == 200) {
        print('Phone number and Firebase token submitted successfully');
      } else {
        print('Failed to submit to API: ${response.statusCode}');
      }
    } catch (e) {
      print('Error submitting to API: $e');
    }
  }

  bool _isValidIranianNumber(String number) {
    // Remove any non-digit characters
    String cleanNumber = number.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it's a valid Iranian mobile number
    return cleanNumber.length == 11 && cleanNumber.startsWith('09');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _showDetailedSnackBar(String title, String subtitle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        duration: const Duration(seconds: 4),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('انحراف پیامک'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'شماره موبایل ایرانی را وارد کنید',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            if (_savedPhoneNumber != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    const Text(
                      'شماره ذخیره شده:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      _savedPhoneNumber!,
                      style: const TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'نظارت بر پیامک فعال است',
                      style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
            TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(11),
              ],
              decoration: const InputDecoration(
                labelText: 'شماره موبایل',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _savePhoneNumber,
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('تایید', style: TextStyle(fontSize: 18)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}


